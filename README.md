# MathMentor - 8th Grade California Math Tutor

An AI-powered math tutor specifically designed for 8th grade California curriculum with voice interaction, memory management, and web search capabilities.

## Features

- 🧮 **8th Grade CA Curriculum Aligned**: Covers all California 8th grade math standards
- 👩‍🏫 **Teacher-Like Behavior**: Acts like a real teacher - asks questions, guides discovery, doesn't just give answers
- 🎤 **Voice Interaction**: Speak to chat using Web Speech API
- 🔊 **Text-to-Speech**: AI responses are spoken using ElevenLabs
- 🧠 **Advanced Memory**: Full conversation history access with automatic memory management
- 🔍 **Web Search**: Search educational resources for additional help
- 💬 **Memory Commands**: `index_memory`, `view_memory`, `clear_memory`
- 🧮 **Built-in Calculator**: Full-featured calculator with mathematical functions
- 📊 **Graphing Tool**: Plot functions, points, and geometric transformations
- 🎨 **Modern UI**: Beautiful, responsive interface with integrated tools
- 🚫 **No Procrastination**: Gently redirects students back to learning, doesn't let them avoid practice
- 🔇 **Smart Speech Control**: Only speaks for teaching, not for memory/tool operations

## Curriculum Coverage

### Number System
- Rational and irrational numbers
- Square roots and cube roots
- Approximating irrational numbers

### Expressions and Equations
- Integer exponents and scientific notation
- Linear equations and inequalities
- Systems of linear equations

### Functions
- Function notation and evaluation
- Linear functions and graphing
- Rate of change and slope

### Geometry
- Transformations (rotations, reflections, translations, dilations)
- Pythagorean Theorem
- Volume formulas

### Statistics and Probability
- Scatter plots and correlation
- Linear models and trend lines
- Bivariate data analysis

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   - The API keys are already configured in `config.py`
   - OpenAI API Key: `********************************************************************************************************************************************************************`
   - ElevenLabs API Key: `***************************************************`
   - ElevenLabs Voice ID: `JBFqnCBsd6RMkjVDRZzb`

3. **Run the application:**
   ```bash
   python app.py
   ```

4. **Open your browser:**
   Navigate to `http://localhost:5000`

## Usage

### Teacher-Like Interaction
- **MathMentor acts like a real teacher**: It will ask you questions to guide your learning
- **No direct answers**: It won't just give you answers - it helps you discover them through questioning
- **Need the answer?**: Say "I really need the answer" if you're truly stuck
- **No procrastination**: It gently redirects you back to learning if you try to avoid practice
- **Full memory**: It remembers your entire conversation to track progress and identify knowledge gaps

### Basic Chat
- Type your math questions in the input field
- Click the microphone button to speak your question
- The AI will respond with guiding questions and speak the response

### Memory Commands
- `index_memory [topic]` - Search conversation history for specific topics
- `view_memory [start] [count]` - View conversation history (default: 0 10)
- `clear_memory` - Clear all conversation history

### Calculator & Graphing Tools
- **Calculator**: Click the calculator button for a full-featured calculator
- **Graphing Tool**: Click the graphing button to plot functions and points
- **AI Integration**: The AI can automatically use these tools by executing commands like:
  - `calculate 2*3+5` - Performs calculations
  - `graph y=2x+1 -10 10` - Plots functions
  - `plot_points [(1,2),(3,4)]` - Plots specific points
  - `transform reflection y=x^2 x-axis` - Applies geometric transformations

### Smart Speech Control
- **Teaching responses**: Spoken aloud for learning
- **Memory operations**: Silent (not spoken)
- **Tool results**: Only calculation summaries are spoken

### Topic Shortcuts
Click on any topic in the sidebar to get started with that subject:
- Number System
- Expressions & Equations
- Functions
- Geometry
- Statistics & Probability

### Tools
- **Web Search**: Search educational resources for additional help
- **View Memory**: Quick access to conversation history
- **Clear Chat**: Clear the conversation

## File Structure

```
mathmentor/
├── app.py                 # Main Flask application
├── config.py             # Configuration and API keys
├── memory_manager.py     # JSON conversation storage
├── curriculum.py         # 8th grade CA curriculum data
├── requirements.txt      # Python dependencies
├── conversation_history.json  # Persistent conversation storage
├── templates/
│   └── index.html        # Main HTML template
└── static/
    ├── style.css         # CSS styles
    └── script.js         # JavaScript functionality
```

## API Endpoints

- `GET /` - Main application page
- `POST /chat` - Send message to AI tutor
- `POST /text-to-speech` - Convert text to speech
- `POST /web-search` - Search educational resources
- `GET /memory-stats` - Get conversation statistics

## Technologies Used

- **Backend**: Flask, OpenAI GPT-4o-mini, ElevenLabs TTS
- **Frontend**: HTML5, CSS3, JavaScript, Web Speech API
- **Memory**: JSON file storage with indexing
- **Voice**: ElevenLabs API for high-quality TTS

## Browser Compatibility

- Chrome/Edge: Full functionality including voice recognition
- Firefox/Safari: Text chat and TTS (voice recognition may be limited)

## Troubleshooting

1. **Voice recognition not working**: Ensure you're using Chrome/Edge and have granted microphone permissions
2. **TTS not working**: Check ElevenLabs API key and internet connection
3. **Chat not responding**: Verify OpenAI API key and check console for errors

## License

This project is for educational purposes. Please ensure you comply with OpenAI and ElevenLabs terms of service.
