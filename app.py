from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from openai import OpenAI
import requests
import json
import io
import base64
from elevenlabs import generate, set_api_key
import urllib.parse
import math
import numpy as np
from config import Config
from memory_manager import MemoryManager
from curriculum import get_curriculum_context
import re

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize APIs
openai_client = OpenAI(api_key=Config.OPENAI_API_KEY)
set_api_key(Config.ELEVENLABS_API_KEY)

# Initialize memory manager
memory_manager = MemoryManager(Config.CONVERSATION_FILE)

# System prompt for the math tutor
SYSTEM_PROMPT = f"""You are <PERSON><PERSON><PERSON><PERSON>, an expert 8th grade mathematics tutor specializing in the California curriculum. You act like a real teacher - asking questions, guiding discovery, and helping students learn through practice.

{get_curriculum_context()}

TEACHING PHILOSOPHY:
- Act like a real teacher: Ask questions to guide learning, don't just give answers
- Use the Socratic method: Lead students to discover answers through questioning
- Only give direct answers when the student explicitly says they "really need the answer" or similar
- Don't let students postpone or avoid practice - gently redirect them back to learning
- Create practice problems and check understanding frequently
- Be encouraging but maintain academic rigor

AGENT CAPABILITIES:
You have full access to conversation history and can execute memory commands automatically when needed:
- index_memory [query]: Search conversation history for specific topics
- view_memory [start] [count]: View conversation history
- clear_memory: Clear conversation history

BEHAVIOR GUIDELINES:
1. When a student asks a question, first ask them what they think or know about the topic
2. Guide them through problems step-by-step with questions like "What's the first step?" or "What do you notice about this equation?"
3. If they get stuck, give hints rather than answers
4. Create follow-up practice problems to reinforce learning
5. Use conversation history to track progress and identify knowledge gaps
6. Don't let students avoid practice - redirect procrastination back to learning
7. Only give direct answers when explicitly requested with phrases like "I really need the answer"

Always be encouraging and adapt your teaching style to the student's needs, but maintain the role of an active teacher who guides learning.
"""

@app.route('/')
def index():
    return render_template('index.html')

def safe_eval(expression):
    """Safely evaluate mathematical expressions"""
    # Allow only safe mathematical operations
    allowed_names = {
        k: v for k, v in math.__dict__.items() if not k.startswith("__")
    }
    allowed_names.update({"abs": abs, "round": round, "min": min, "max": max})

    try:
        # Remove any potentially dangerous characters
        safe_expr = re.sub(r'[^0-9+\-*/().,\s\w]', '', expression)
        result = eval(safe_expr, {"__builtins__": {}}, allowed_names)
        return str(result)
    except:
        return "Error: Invalid mathematical expression"

def execute_tool_command(command, memory_manager):
    """Execute tool commands and return results"""
    command = command.strip()

    # Memory commands
    if command.lower().startswith('index_memory'):
        query = command[12:].strip()
        results = memory_manager.index_memory(query)
        response = f"[Memory Search] Found {len(results)} messages related to '{query}':\n\n"
        for i, msg in enumerate(results[:5], 1):
            response += f"{i}. [{msg['timestamp'][:10]}] {msg['role']}: {msg['content'][:100]}...\n"
        return response, False  # Don't speak memory results

    elif command.lower().startswith('view_memory'):
        parts = command.split()
        start = int(parts[1]) if len(parts) > 1 else 0
        count = int(parts[2]) if len(parts) > 2 else 10
        results = memory_manager.view_memory(start, count)
        response = f"[Memory View] Showing {len(results)} messages from index {start}:\n\n"
        for i, msg in enumerate(results, start):
            response += f"{i}. [{msg['timestamp'][:16]}] {msg['role']}: {msg['content'][:150]}...\n"
        return response, False  # Don't speak memory results

    elif command.lower() == 'clear_memory':
        memory_manager.clear_memory()
        return "[Memory] Conversation memory has been cleared.", False

    # Calculator commands
    elif command.lower().startswith('calculate'):
        expression = command[9:].strip()
        result = safe_eval(expression)
        return f"[Calculator] {expression} = {result}", True

    # Graphing commands
    elif command.lower().startswith('graph'):
        parts = command[5:].strip().split()
        if len(parts) >= 3:
            function = parts[0]
            xmin = float(parts[1])
            xmax = float(parts[2])

            # Generate graph data
            try:
                x_values = np.linspace(xmin, xmax, 200)
                y_values = []

                # Parse function (simple cases)
                func_str = function.replace('y=', '').replace('Y=', '')

                for x in x_values:
                    try:
                        # Replace x with actual value
                        expr = func_str.replace('x', f'({x})')
                        # Handle common functions
                        expr = expr.replace('^', '**')  # Convert ^ to **
                        y = eval(expr, {"__builtins__": {}}, {"sin": np.sin, "cos": np.cos, "tan": np.tan, "sqrt": np.sqrt, "abs": abs, "log": np.log, "exp": np.exp})
                        y_values.append(float(y))
                    except:
                        y_values.append(None)

                # Create graph HTML
                graph_html = f"""
                <div class="inline-graph">
                    <h4>Graph: {function}</h4>
                    <canvas id="graph_{hash(function)}" width="400" height="300" style="border: 1px solid #ccc; border-radius: 8px;"></canvas>
                    <script>
                        (function() {{
                            const canvas = document.getElementById('graph_{hash(function)}');
                            const ctx = canvas.getContext('2d');
                            const xValues = {x_values.tolist()};
                            const yValues = {y_values};

                            // Clear canvas
                            ctx.clearRect(0, 0, canvas.width, canvas.height);

                            // Draw graph
                            const padding = 30;
                            const width = canvas.width - 2 * padding;
                            const height = canvas.height - 2 * padding;

                            const xMin = Math.min(...xValues);
                            const xMax = Math.max(...xValues);
                            const yMin = Math.min(...yValues.filter(y => y !== null));
                            const yMax = Math.max(...yValues.filter(y => y !== null));

                            // Draw axes
                            ctx.strokeStyle = '#333';
                            ctx.lineWidth = 1;
                            ctx.beginPath();

                            // X-axis
                            const yZero = padding + height - (0 - yMin) / (yMax - yMin) * height;
                            ctx.moveTo(padding, yZero);
                            ctx.lineTo(padding + width, yZero);

                            // Y-axis
                            const xZero = padding + (0 - xMin) / (xMax - xMin) * width;
                            ctx.moveTo(xZero, padding);
                            ctx.lineTo(xZero, padding + height);
                            ctx.stroke();

                            // Plot function
                            ctx.strokeStyle = '#667eea';
                            ctx.lineWidth = 2;
                            ctx.beginPath();

                            let firstPoint = true;
                            for (let i = 0; i < xValues.length; i++) {{
                                if (yValues[i] !== null && !isNaN(yValues[i])) {{
                                    const x = padding + (xValues[i] - xMin) / (xMax - xMin) * width;
                                    const y = padding + height - (yValues[i] - yMin) / (yMax - yMin) * height;

                                    if (firstPoint) {{
                                        ctx.moveTo(x, y);
                                        firstPoint = false;
                                    }} else {{
                                        ctx.lineTo(x, y);
                                    }}
                                }}
                            }}
                            ctx.stroke();
                        }})();
                    </script>
                </div>
                """

                return f"[Graph] {graph_html}", True

            except Exception as e:
                return f"[Graph] Error plotting {function}: {str(e)}", True
        return "[Graph] Error: Invalid graph command format", True

    elif command.lower().startswith('plot_points'):
        points_str = command[11:].strip()
        try:
            # Parse points string like "[(1,2),(3,4),(5,6)]"
            import ast
            points = ast.literal_eval(points_str)

            graph_html = f"""
            <div class="inline-graph">
                <h4>Points: {points_str}</h4>
                <canvas id="points_{hash(points_str)}" width="400" height="300" style="border: 1px solid #ccc; border-radius: 8px;"></canvas>
                <script>
                    (function() {{
                        const canvas = document.getElementById('points_{hash(points_str)}');
                        const ctx = canvas.getContext('2d');
                        const points = {points};

                        // Clear canvas
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        const padding = 30;
                        const width = canvas.width - 2 * padding;
                        const height = canvas.height - 2 * padding;

                        const xValues = points.map(p => p[0]);
                        const yValues = points.map(p => p[1]);
                        const xMin = Math.min(...xValues) - 1;
                        const xMax = Math.max(...xValues) + 1;
                        const yMin = Math.min(...yValues) - 1;
                        const yMax = Math.max(...yValues) + 1;

                        // Draw axes
                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 1;
                        ctx.beginPath();

                        const yZero = padding + height - (0 - yMin) / (yMax - yMin) * height;
                        ctx.moveTo(padding, yZero);
                        ctx.lineTo(padding + width, yZero);

                        const xZero = padding + (0 - xMin) / (xMax - xMin) * width;
                        ctx.moveTo(xZero, padding);
                        ctx.lineTo(xZero, padding + height);
                        ctx.stroke();

                        // Plot points
                        ctx.fillStyle = '#e53e3e';
                        points.forEach(point => {{
                            const x = padding + (point[0] - xMin) / (xMax - xMin) * width;
                            const y = padding + height - (point[1] - yMin) / (yMax - yMin) * height;

                            ctx.beginPath();
                            ctx.arc(x, y, 4, 0, 2 * Math.PI);
                            ctx.fill();

                            // Label point
                            ctx.fillStyle = '#333';
                            ctx.font = '12px Arial';
                            ctx.fillText(`(${{point[0]}},${{point[1]}})`, x + 5, y - 5);
                            ctx.fillStyle = '#e53e3e';
                        }});
                    }})();
                </script>
            </div>
            """

            return f"[Graph] {graph_html}", True

        except Exception as e:
            return f"[Graph] Error plotting points: {str(e)}", True

    elif command.lower().startswith('transform'):
        transform_info = command[9:].strip()
        return f"[Graph] Applying transformation: {transform_info}", True

    return None, True

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '')

        # Add user message to memory
        memory_manager.add_message('user', user_message)

        # Check for explicit tool commands from user
        if user_message.lower().startswith(('index_memory', 'view_memory', 'clear_memory', 'calculate', 'graph', 'plot_points', 'transform')):
            response, should_speak = execute_tool_command(user_message, memory_manager)
            memory_manager.add_message('assistant', response)
            return jsonify({
                'response': response,
                'should_speak': should_speak
            })

        # Get ALL conversation history for full context
        all_messages = memory_manager.get_recent_messages(50)  # Get more history

        # Prepare messages for OpenAI with full context
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]

        # Add conversation history for full context
        for msg in all_messages[-20:]:  # Last 20 messages for better context
            if msg['role'] in ['user', 'assistant']:
                messages.append({"role": msg['role'], "content": msg['content']})

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        # Enhanced system message with memory access instructions
        enhanced_system = SYSTEM_PROMPT + f"""

TOOL ACCESS: You can access tools by including commands in your response:

MEMORY ACCESS:
- Search for specific topics: Include "EXECUTE: index_memory [topic]" in your response
- View recent conversation: Include "EXECUTE: view_memory [start] [count]" in your response
- Check student progress: Use memory commands to review past interactions

CALCULATOR ACCESS:
- Perform calculations: Include "EXECUTE: calculate [expression]" in your response
- Example: "EXECUTE: calculate 2*3+5" will compute and show the result

GRAPHING ACCESS:
- Plot functions: Include "EXECUTE: graph [function] [xmin] [xmax]" in your response
- Plot points: Include "EXECUTE: plot_points [(x1,y1),(x2,y2),...]" in your response
- Geometric transformations: Include "EXECUTE: transform [type] [function] [parameters]" in your response
- Examples:
  - "EXECUTE: graph y=2x+1 -10 10" (plots linear function)
  - "EXECUTE: plot_points [(1,2),(3,4),(5,6)]" (plots specific points)
  - "EXECUTE: transform reflection y=x^2 x-axis" (reflects parabola over x-axis)

WHEN TO USE GRAPHING:
- Teaching about functions (linear, quadratic, etc.)
- Showing geometric transformations (reflections, rotations, translations)
- Visualizing coordinate geometry concepts
- Demonstrating slope, y-intercept, and function behavior
- Comparing before/after transformations
- Any time a visual would help student understanding

USE GRAPHS FREQUENTLY to enhance learning - students learn better with visual representations!

The system will automatically execute these commands and provide visual results.
"""

        messages[0]["content"] = enhanced_system

        # Get response from OpenAI
        response = openai_client.chat.completions.create(
            model=Config.OPENAI_MODEL,
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )

        assistant_response = response.choices[0].message.content

        # Check if the assistant wants to execute tool commands
        if "EXECUTE:" in assistant_response:
            lines = assistant_response.split('\n')
            processed_response = []
            overall_should_speak = True

            for line in lines:
                if line.strip().startswith('EXECUTE:'):
                    command = line.strip()[8:].strip()  # Remove "EXECUTE: "
                    tool_result, command_should_speak = execute_tool_command(command, memory_manager)
                    if tool_result:
                        processed_response.append(tool_result)
                        if not command_should_speak:
                            overall_should_speak = False
                else:
                    processed_response.append(line)

            assistant_response = '\n'.join(processed_response)
            should_speak = overall_should_speak

        # Add assistant response to memory
        memory_manager.add_message('assistant', assistant_response)

        # Determine if response should be spoken
        should_speak = True
        if any(keyword in assistant_response.lower() for keyword in ['memory search result', 'found', 'messages related', 'showing', 'messages from index']):
            should_speak = False

        return jsonify({
            'response': assistant_response,
            'should_speak': should_speak
        })

    except Exception as e:
        error_msg = f"Sorry, I encountered an error: {str(e)}"
        memory_manager.add_message('assistant', error_msg)
        return jsonify({
            'response': error_msg,
            'should_speak': True
        }), 500

@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    try:
        data = request.json
        text = data.get('text', '')
        
        # Generate audio using ElevenLabs
        audio = generate(
            text=text,
            voice=Config.ELEVENLABS_VOICE_ID,
            model="eleven_monolingual_v1"
        )
        
        # Convert to base64 for frontend
        audio_base64 = base64.b64encode(audio).decode('utf-8')
        
        return jsonify({'audio': audio_base64})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/web-search', methods=['POST'])
def web_search():
    try:
        data = request.json
        query = data.get('query', '')

        # Create search URLs for different educational resources
        search_results = [
            f"https://www.khanacademy.org/search?page_search_query={urllib.parse.quote(query + ' 8th grade math')}",
            f"https://www.mathplayground.com/search.html?q={urllib.parse.quote(query)}",
            f"https://www.ixl.com/search/learning?query={urllib.parse.quote(query + ' grade 8')}",
            f"https://www.google.com/search?q={urllib.parse.quote(query + ' 8th grade math site:edu')}",
            f"https://www.purplemath.com/modules/search.php?q={urllib.parse.quote(query)}"
        ]

        return jsonify({'results': search_results})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/calculate', methods=['POST'])
def calculate():
    try:
        data = request.json
        expression = data.get('expression', '')
        result = safe_eval(expression)
        return jsonify({'result': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/graph', methods=['POST'])
def graph():
    try:
        data = request.json
        function = data.get('function', '')
        xmin = data.get('xmin', -10)
        xmax = data.get('xmax', 10)

        # Generate points for the function
        x_values = np.linspace(xmin, xmax, 200)
        y_values = []

        # Parse and evaluate the function
        func_str = function.replace('y=', '').replace('x', '*x').replace('**', '^')

        for x in x_values:
            try:
                # Replace x with actual value and evaluate
                expr = func_str.replace('x', str(x))
                y = safe_eval(expr)
                y_values.append(float(y) if y != "Error: Invalid mathematical expression" else None)
            except:
                y_values.append(None)

        return jsonify({
            'x_values': x_values.tolist(),
            'y_values': y_values,
            'function': function
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/plot-points', methods=['POST'])
def plot_points():
    try:
        data = request.json
        points = data.get('points', [])
        return jsonify({'points': points})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/transform', methods=['POST'])
def transform():
    try:
        data = request.json
        transform_type = data.get('type', '')
        function = data.get('function', '')
        parameters = data.get('parameters', {})

        # This would implement geometric transformations
        # For now, return the transformation info
        return jsonify({
            'type': transform_type,
            'function': function,
            'parameters': parameters
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/memory-stats')
def memory_stats():
    stats = memory_manager.get_conversation_summary()
    return jsonify(stats)

if __name__ == '__main__':
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
