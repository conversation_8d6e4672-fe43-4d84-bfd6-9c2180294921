from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import openai
import requests
import json
import io
import base64
from elevenlabs import generate, set_api_key
import urllib.parse
from config import Config
from memory_manager import MemoryManager
from curriculum import get_curriculum_context
import re

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize APIs
openai.api_key = Config.OPENAI_API_KEY
set_api_key(Config.ELEVENLABS_API_KEY)

# Initialize memory manager
memory_manager = MemoryManager(Config.CONVERSATION_FILE)

# System prompt for the math tutor
SYSTEM_PROMPT = f"""You are <PERSON><PERSON><PERSON><PERSON>, an expert 8th grade mathematics tutor specializing in the California curriculum. You are friendly, encouraging, and patient.

{get_curriculum_context()}

Your capabilities include:
1. Teaching and explaining 8th grade math concepts
2. Solving problems step-by-step
3. Creating practice problems
4. Using memory commands: index_memory, view_memory, clear_memory
5. Web search for additional resources
6. Providing visual explanations when helpful

Memory Commands:
- index_memory [query]: Search conversation history for specific topics
- view_memory [start] [count]: View conversation history 
- clear_memory: Clear conversation history

Always be encouraging and adapt your teaching style to the student's needs. Break down complex problems into manageable steps.
"""

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '')
        
        # Add user message to memory
        memory_manager.add_message('user', user_message)
        
        # Check for memory commands
        if user_message.lower().startswith('index_memory'):
            query = user_message[12:].strip()
            results = memory_manager.index_memory(query)
            response = f"Found {len(results)} messages related to '{query}':\n\n"
            for i, msg in enumerate(results[:5], 1):
                response += f"{i}. [{msg['timestamp'][:10]}] {msg['role']}: {msg['content'][:100]}...\n"
            memory_manager.add_message('assistant', response)
            return jsonify({'response': response})
        
        elif user_message.lower().startswith('view_memory'):
            parts = user_message.split()
            start = int(parts[1]) if len(parts) > 1 else 0
            count = int(parts[2]) if len(parts) > 2 else 10
            results = memory_manager.view_memory(start, count)
            response = f"Showing {len(results)} messages from index {start}:\n\n"
            for i, msg in enumerate(results, start):
                response += f"{i}. [{msg['timestamp'][:16]}] {msg['role']}: {msg['content'][:150]}...\n"
            memory_manager.add_message('assistant', response)
            return jsonify({'response': response})
        
        elif user_message.lower() == 'clear_memory':
            memory_manager.clear_memory()
            response = "Conversation memory has been cleared."
            memory_manager.add_message('assistant', response)
            return jsonify({'response': response})
        
        # Get recent conversation for context
        recent_messages = memory_manager.get_recent_messages(10)
        
        # Prepare messages for OpenAI
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]
        
        # Add recent conversation context
        for msg in recent_messages[-8:]:  # Last 8 messages for context
            if msg['role'] in ['user', 'assistant']:
                messages.append({"role": msg['role'], "content": msg['content']})
        
        # Add current user message
        messages.append({"role": "user", "content": user_message})
        
        # Get response from OpenAI
        response = openai.ChatCompletion.create(
            model=Config.OPENAI_MODEL,
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )
        
        assistant_response = response.choices[0].message.content
        
        # Add assistant response to memory
        memory_manager.add_message('assistant', assistant_response)
        
        return jsonify({'response': assistant_response})
        
    except Exception as e:
        error_msg = f"Sorry, I encountered an error: {str(e)}"
        memory_manager.add_message('assistant', error_msg)
        return jsonify({'response': error_msg}), 500

@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    try:
        data = request.json
        text = data.get('text', '')
        
        # Generate audio using ElevenLabs
        audio = generate(
            text=text,
            voice=Config.ELEVENLABS_VOICE_ID,
            model="eleven_monolingual_v1"
        )
        
        # Convert to base64 for frontend
        audio_base64 = base64.b64encode(audio).decode('utf-8')
        
        return jsonify({'audio': audio_base64})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/web-search', methods=['POST'])
def web_search():
    try:
        data = request.json
        query = data.get('query', '')

        # Create search URLs for different educational resources
        search_results = [
            f"https://www.khanacademy.org/search?page_search_query={urllib.parse.quote(query + ' 8th grade math')}",
            f"https://www.mathplayground.com/search.html?q={urllib.parse.quote(query)}",
            f"https://www.ixl.com/search/learning?query={urllib.parse.quote(query + ' grade 8')}",
            f"https://www.google.com/search?q={urllib.parse.quote(query + ' 8th grade math site:edu')}",
            f"https://www.purplemath.com/modules/search.php?q={urllib.parse.quote(query)}"
        ]

        return jsonify({'results': search_results})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/memory-stats')
def memory_stats():
    stats = memory_manager.get_conversation_summary()
    return jsonify(stats)

if __name__ == '__main__':
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
