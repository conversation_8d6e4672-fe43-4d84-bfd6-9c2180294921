from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from openai import OpenAI
import requests
import json
import io
import base64
from elevenlabs import generate, set_api_key
import urllib.parse
from config import Config
from memory_manager import MemoryManager
from curriculum import get_curriculum_context
import re

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize APIs
openai_client = OpenAI(api_key=Config.OPENAI_API_KEY)
set_api_key(Config.ELEVENLABS_API_KEY)

# Initialize memory manager
memory_manager = MemoryManager(Config.CONVERSATION_FILE)

# System prompt for the math tutor
SYSTEM_PROMPT = f"""You are <PERSON><PERSON><PERSON><PERSON>, an expert 8th grade mathematics tutor specializing in the California curriculum. You act like a real teacher - asking questions, guiding discovery, and helping students learn through practice.

{get_curriculum_context()}

TEACHING PHILOSOPHY:
- Act like a real teacher: Ask questions to guide learning, don't just give answers
- Use the Socratic method: Lead students to discover answers through questioning
- Only give direct answers when the student explicitly says they "really need the answer" or similar
- Don't let students postpone or avoid practice - gently redirect them back to learning
- Create practice problems and check understanding frequently
- Be encouraging but maintain academic rigor

AGENT CAPABILITIES:
You have full access to conversation history and can execute memory commands automatically when needed:
- index_memory [query]: Search conversation history for specific topics
- view_memory [start] [count]: View conversation history
- clear_memory: Clear conversation history

BEHAVIOR GUIDELINES:
1. When a student asks a question, first ask them what they think or know about the topic
2. Guide them through problems step-by-step with questions like "What's the first step?" or "What do you notice about this equation?"
3. If they get stuck, give hints rather than answers
4. Create follow-up practice problems to reinforce learning
5. Use conversation history to track progress and identify knowledge gaps
6. Don't let students avoid practice - redirect procrastination back to learning
7. Only give direct answers when explicitly requested with phrases like "I really need the answer"

Always be encouraging and adapt your teaching style to the student's needs, but maintain the role of an active teacher who guides learning.
"""

@app.route('/')
def index():
    return render_template('index.html')

def execute_memory_command(command, memory_manager):
    """Execute memory commands and return results"""
    if command.lower().startswith('index_memory'):
        query = command[12:].strip()
        results = memory_manager.index_memory(query)
        response = f"Found {len(results)} messages related to '{query}':\n\n"
        for i, msg in enumerate(results[:5], 1):
            response += f"{i}. [{msg['timestamp'][:10]}] {msg['role']}: {msg['content'][:100]}...\n"
        return response

    elif command.lower().startswith('view_memory'):
        parts = command.split()
        start = int(parts[1]) if len(parts) > 1 else 0
        count = int(parts[2]) if len(parts) > 2 else 10
        results = memory_manager.view_memory(start, count)
        response = f"Showing {len(results)} messages from index {start}:\n\n"
        for i, msg in enumerate(results, start):
            response += f"{i}. [{msg['timestamp'][:16]}] {msg['role']}: {msg['content'][:150]}...\n"
        return response

    elif command.lower() == 'clear_memory':
        memory_manager.clear_memory()
        return "Conversation memory has been cleared."

    return None

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        user_message = data.get('message', '')

        # Add user message to memory
        memory_manager.add_message('user', user_message)

        # Check for explicit memory commands from user
        if user_message.lower().startswith(('index_memory', 'view_memory', 'clear_memory')):
            response = execute_memory_command(user_message, memory_manager)
            memory_manager.add_message('assistant', response)
            return jsonify({'response': response})

        # Get ALL conversation history for full context
        all_messages = memory_manager.get_recent_messages(50)  # Get more history

        # Prepare messages for OpenAI with full context
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]

        # Add conversation history for full context
        for msg in all_messages[-20:]:  # Last 20 messages for better context
            if msg['role'] in ['user', 'assistant']:
                messages.append({"role": msg['role'], "content": msg['content']})

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        # Enhanced system message with memory access instructions
        enhanced_system = SYSTEM_PROMPT + f"""

MEMORY ACCESS: You can access conversation history by including memory commands in your response. When you need to:
- Search for specific topics: Include "EXECUTE: index_memory [topic]" in your response
- View recent conversation: Include "EXECUTE: view_memory [start] [count]" in your response
- Check student progress: Use memory commands to review past interactions

The system will automatically execute these commands and provide you with the results.
"""

        messages[0]["content"] = enhanced_system

        # Get response from OpenAI
        response = openai_client.chat.completions.create(
            model=Config.OPENAI_MODEL,
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )

        assistant_response = response.choices[0].message.content

        # Check if the assistant wants to execute memory commands
        if "EXECUTE:" in assistant_response:
            lines = assistant_response.split('\n')
            processed_response = []

            for line in lines:
                if line.strip().startswith('EXECUTE:'):
                    command = line.strip()[8:].strip()  # Remove "EXECUTE: "
                    memory_result = execute_memory_command(command, memory_manager)
                    if memory_result:
                        processed_response.append(f"[Memory Search Result: {memory_result}]")
                else:
                    processed_response.append(line)

            assistant_response = '\n'.join(processed_response)

        # Add assistant response to memory
        memory_manager.add_message('assistant', assistant_response)

        return jsonify({'response': assistant_response})

    except Exception as e:
        error_msg = f"Sorry, I encountered an error: {str(e)}"
        memory_manager.add_message('assistant', error_msg)
        return jsonify({'response': error_msg}), 500

@app.route('/text-to-speech', methods=['POST'])
def text_to_speech():
    try:
        data = request.json
        text = data.get('text', '')
        
        # Generate audio using ElevenLabs
        audio = generate(
            text=text,
            voice=Config.ELEVENLABS_VOICE_ID,
            model="eleven_monolingual_v1"
        )
        
        # Convert to base64 for frontend
        audio_base64 = base64.b64encode(audio).decode('utf-8')
        
        return jsonify({'audio': audio_base64})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/web-search', methods=['POST'])
def web_search():
    try:
        data = request.json
        query = data.get('query', '')

        # Create search URLs for different educational resources
        search_results = [
            f"https://www.khanacademy.org/search?page_search_query={urllib.parse.quote(query + ' 8th grade math')}",
            f"https://www.mathplayground.com/search.html?q={urllib.parse.quote(query)}",
            f"https://www.ixl.com/search/learning?query={urllib.parse.quote(query + ' grade 8')}",
            f"https://www.google.com/search?q={urllib.parse.quote(query + ' 8th grade math site:edu')}",
            f"https://www.purplemath.com/modules/search.php?q={urllib.parse.quote(query)}"
        ]

        return jsonify({'results': search_results})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/memory-stats')
def memory_stats():
    stats = memory_manager.get_conversation_summary()
    return jsonify(stats)

if __name__ == '__main__':
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
