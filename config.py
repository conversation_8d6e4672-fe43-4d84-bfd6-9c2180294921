import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys
    OPENAI_API_KEY = "********************************************************************************************************************************************************************"
    ELEVENLABS_API_KEY = "***************************************************"
    
    # ElevenLabs Settings
    ELEVENLABS_VOICE_ID = "JBFqnCBsd6RMkjVDRZzb"
    
    # OpenAI Settings
    OPENAI_MODEL = "gpt-4o-mini"
    
    # File Paths
    CONVERSATION_FILE = "conversation_history.json"
    
    # Flask Settings
    SECRET_KEY = "your-secret-key-here"
    DEBUG = True
