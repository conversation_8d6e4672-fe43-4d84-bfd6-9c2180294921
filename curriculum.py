# 8th Grade California Mathematics Curriculum Standards

CALIFORNIA_8TH_GRADE_STANDARDS = {
    "number_system": {
        "title": "The Number System",
        "standards": [
            "8.NS.1: Know that numbers that are not rational are called irrational",
            "8.NS.2: Use rational approximations of irrational numbers",
        ],
        "topics": [
            "Rational and irrational numbers",
            "Square roots and cube roots",
            "Approximating irrational numbers",
            "Comparing and ordering real numbers"
        ]
    },
    "expressions_equations": {
        "title": "Expressions and Equations",
        "standards": [
            "8.EE.1: Work with radicals and integer exponents",
            "8.EE.2: Use square root and cube root symbols",
            "8.EE.3: Use numbers expressed in scientific notation",
            "8.EE.4: Perform operations with numbers in scientific notation",
            "8.EE.5: Graph proportional relationships",
            "8.EE.6: Use similar triangles to explain slope",
            "8.EE.7: Solve linear equations in one variable",
            "8.EE.8: Analyze and solve systems of linear equations"
        ],
        "topics": [
            "Integer exponents and scientific notation",
            "Square roots and cube roots",
            "Linear equations and inequalities",
            "Systems of linear equations",
            "Graphing linear equations",
            "Slope and y-intercept"
        ]
    },
    "functions": {
        "title": "Functions",
        "standards": [
            "8.F.1: Understand that a function is a rule",
            "8.F.2: Compare properties of two functions",
            "8.F.3: Interpret the equation y = mx + b",
            "8.F.4: Construct a function to model a linear relationship",
            "8.F.5: Describe the functional relationship between quantities"
        ],
        "topics": [
            "Function notation and evaluation",
            "Linear functions",
            "Rate of change and slope",
            "Function tables and graphs",
            "Modeling with functions"
        ]
    },
    "geometry": {
        "title": "Geometry",
        "standards": [
            "8.G.1: Verify properties of rotations, reflections, and translations",
            "8.G.2: Understand that 2D figures are congruent if one can be obtained from the other",
            "8.G.3: Describe the effect of dilations, translations, rotations, and reflections",
            "8.G.4: Understand that 2D figures are similar if one can be obtained from the other",
            "8.G.5: Use informal arguments to establish facts about angles",
            "8.G.6: Explain a proof of the Pythagorean Theorem",
            "8.G.7: Apply the Pythagorean Theorem",
            "8.G.8: Apply the Pythagorean Theorem in the coordinate plane",
            "8.G.9: Know the formulas for volumes of cones, cylinders, and spheres"
        ],
        "topics": [
            "Transformations (rotations, reflections, translations, dilations)",
            "Congruence and similarity",
            "Pythagorean Theorem",
            "Volume formulas",
            "Coordinate geometry",
            "Angle relationships"
        ]
    },
    "statistics_probability": {
        "title": "Statistics and Probability",
        "standards": [
            "8.SP.1: Construct and interpret scatter plots",
            "8.SP.2: Know that straight lines are widely used to model relationships",
            "8.SP.3: Use the equation of a linear model to solve problems",
            "8.SP.4: Understand that patterns of association can also be seen in bivariate categorical data"
        ],
        "topics": [
            "Scatter plots and correlation",
            "Linear models and trend lines",
            "Two-way tables",
            "Bivariate data analysis"
        ]
    }
}

def get_curriculum_context():
    """Return a formatted string of the curriculum for the AI tutor."""
    context = "8th Grade California Mathematics Curriculum:\n\n"
    
    for domain_key, domain in CALIFORNIA_8TH_GRADE_STANDARDS.items():
        context += f"{domain['title']}:\n"
        context += f"Standards: {', '.join(domain['standards'])}\n"
        context += f"Key Topics: {', '.join(domain['topics'])}\n\n"
    
    return context
