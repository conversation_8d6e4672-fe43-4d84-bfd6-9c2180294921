import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

class MemoryManager:
    def __init__(self, conversation_file: str = "conversation_history.json"):
        self.conversation_file = conversation_file
        self.conversation_history = self.load_conversation_history()
    
    def load_conversation_history(self) -> List[Dict[str, Any]]:
        """Load conversation history from JSON file."""
        if os.path.exists(self.conversation_file):
            try:
                with open(self.conversation_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []
    
    def save_conversation_history(self):
        """Save conversation history to JSON file."""
        with open(self.conversation_file, 'w', encoding='utf-8') as f:
            json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
    
    def add_message(self, role: str, content: str, message_type: str = "text", metadata: Optional[Dict] = None):
        """Add a message to the conversation history."""
        message = {
            "timestamp": datetime.now().isoformat(),
            "role": role,
            "content": content,
            "type": message_type,
            "metadata": metadata or {}
        }
        self.conversation_history.append(message)
        self.save_conversation_history()
    
    def get_recent_messages(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get the most recent messages."""
        return self.conversation_history[-count:] if self.conversation_history else []
    
    def search_messages(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for messages containing the query."""
        results = []
        query_lower = query.lower()
        
        for message in reversed(self.conversation_history):
            if query_lower in message['content'].lower():
                results.append(message)
                if len(results) >= limit:
                    break
        
        return results
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        total_messages = len(self.conversation_history)
        user_messages = len([m for m in self.conversation_history if m['role'] == 'user'])
        assistant_messages = len([m for m in self.conversation_history if m['role'] == 'assistant'])
        
        return {
            "total_messages": total_messages,
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "first_message": self.conversation_history[0]['timestamp'] if self.conversation_history else None,
            "last_message": self.conversation_history[-1]['timestamp'] if self.conversation_history else None
        }
    
    def index_memory(self, query: str) -> List[Dict[str, Any]]:
        """Index and search through conversation memory."""
        return self.search_messages(query, limit=10)
    
    def view_memory(self, start_index: int = 0, count: int = 20) -> List[Dict[str, Any]]:
        """View a portion of the conversation memory."""
        end_index = min(start_index + count, len(self.conversation_history))
        return self.conversation_history[start_index:end_index]
    
    def clear_memory(self):
        """Clear all conversation history."""
        self.conversation_history = []
        self.save_conversation_history()
    
    def export_memory(self, filename: str = None) -> str:
        """Export conversation history to a file."""
        if filename is None:
            filename = f"conversation_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
        
        return filename
