class MathTutor {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.voiceButton = document.getElementById('voiceButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.loading = document.getElementById('loading');
        this.voiceStatus = document.getElementById('voiceStatus');
        this.memoryStats = document.getElementById('memoryStats');
        
        this.isListening = false;
        this.recognition = null;
        
        this.initializeEventListeners();
        this.initializeSpeechRecognition();
        this.updateMemoryStats();
    }
    
    initializeEventListeners() {
        // Send message on button click
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Send message on Enter key
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        
        // Voice button
        this.voiceButton.addEventListener('click', () => this.toggleVoiceRecognition());
        
        // Topic buttons
        document.querySelectorAll('.topic-item').forEach(item => {
            item.addEventListener('click', () => {
                const topic = item.dataset.topic;
                this.sendTopicMessage(topic);
            });
        });
        
        // Tool buttons
        document.getElementById('searchBtn').addEventListener('click', () => this.showSearchDialog());
        document.getElementById('memoryBtn').addEventListener('click', () => this.viewMemory());
        document.getElementById('clearBtn').addEventListener('click', () => this.clearChat());
    }
    
    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
            
            this.recognition.onstart = () => {
                this.isListening = true;
                this.voiceButton.classList.add('listening');
                this.voiceStatus.style.display = 'flex';
            };
            
            this.recognition.onend = () => {
                this.isListening = false;
                this.voiceButton.classList.remove('listening');
                this.voiceStatus.style.display = 'none';
            };
            
            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.messageInput.value = transcript;
                this.sendMessage();
            };
            
            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.isListening = false;
                this.voiceButton.classList.remove('listening');
                this.voiceStatus.style.display = 'none';
            };
        } else {
            this.voiceButton.style.display = 'none';
        }
    }
    
    toggleVoiceRecognition() {
        if (!this.recognition) return;
        
        if (this.isListening) {
            this.recognition.stop();
        } else {
            this.recognition.start();
        }
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // Add user message to chat
        this.addMessage('user', message);
        this.messageInput.value = '';
        
        // Show loading
        this.loading.style.display = 'flex';
        
        try {
            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });
            
            const data = await response.json();
            
            // Add assistant response to chat
            this.addMessage('assistant', data.response);
            
            // Speak the response
            this.speakText(data.response);
            
            // Update memory stats
            this.updateMemoryStats();
            
        } catch (error) {
            console.error('Error:', error);
            this.addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
        } finally {
            this.loading.style.display = 'none';
        }
    }
    
    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;
        
        const icon = role === 'user' ? 'fas fa-user' : 'fas fa-robot';
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <i class="${icon}"></i>
                <div class="text">${this.formatMessage(content)}</div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    formatMessage(content) {
        // Convert newlines to <br> and format lists
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    async speakText(text) {
        try {
            // Remove markdown and HTML for cleaner speech
            const cleanText = text
                .replace(/\*\*(.*?)\*\*/g, '$1')
                .replace(/\*(.*?)\*/g, '$1')
                .replace(/`(.*?)`/g, '$1')
                .replace(/<[^>]*>/g, '')
                .replace(/\n/g, ' ');
            
            const response = await fetch('/text-to-speech', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: cleanText })
            });
            
            const data = await response.json();
            
            if (data.audio) {
                const audio = new Audio(`data:audio/mp3;base64,${data.audio}`);
                audio.play();
            }
        } catch (error) {
            console.error('Text-to-speech error:', error);
        }
    }
    
    sendTopicMessage(topic) {
        const topicMessages = {
            'number-system': 'Can you help me understand rational and irrational numbers?',
            'expressions': 'I need help with expressions and equations, especially scientific notation.',
            'functions': 'Can you explain linear functions and how to graph them?',
            'geometry': 'I want to learn about the Pythagorean Theorem and transformations.',
            'statistics': 'Help me understand scatter plots and data analysis.'
        };
        
        const message = topicMessages[topic] || `Tell me about ${topic}`;
        this.messageInput.value = message;
        this.sendMessage();
    }
    
    showSearchDialog() {
        const query = prompt('What would you like to search for?');
        if (query) {
            this.messageInput.value = `Can you search for information about: ${query}`;
            this.sendMessage();
        }
    }
    
    viewMemory() {
        this.messageInput.value = 'view_memory 0 10';
        this.sendMessage();
    }
    
    clearChat() {
        if (confirm('Are you sure you want to clear the conversation history?')) {
            this.messageInput.value = 'clear_memory';
            this.sendMessage();
            
            // Clear the chat display
            setTimeout(() => {
                const messages = this.chatMessages.querySelectorAll('.message:not(:first-child)');
                messages.forEach(msg => msg.remove());
            }, 1000);
        }
    }
    
    async updateMemoryStats() {
        try {
            const response = await fetch('/memory-stats');
            const stats = await response.json();
            
            const messageCount = document.getElementById('messageCount');
            messageCount.textContent = `${stats.total_messages} messages`;
        } catch (error) {
            console.error('Error updating memory stats:', error);
        }
    }
}

// Initialize the math tutor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MathTutor();
});
