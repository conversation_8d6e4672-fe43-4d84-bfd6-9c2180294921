* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 700;
}

.header h1 i {
    color: #667eea;
    margin-right: 0.5rem;
}

.header p {
    color: #718096;
    margin-top: 0.25rem;
}

.stats {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.main-content {
    flex: 1;
    display: flex;
    gap: 1rem;
    padding: 1rem 2rem;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.user-message .message-content {
    flex-direction: row-reverse;
}

.message-content i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message i {
    background: #667eea;
    color: white;
}

.assistant-message i {
    background: #48bb78;
    color: white;
}

.text {
    background: #f7fafc;
    padding: 1rem 1.25rem;
    border-radius: 15px;
    max-width: 80%;
    line-height: 1.6;
}

.user-message .text {
    background: #667eea;
    color: white;
}

.text ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.text code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.input-container {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#messageInput {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

#messageInput:focus {
    border-color: #667eea;
}

.voice-btn, .send-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s;
}

.voice-btn {
    background: #48bb78;
    color: white;
}

.voice-btn:hover {
    background: #38a169;
    transform: scale(1.05);
}

.voice-btn.listening {
    background: #e53e3e;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.send-btn {
    background: #667eea;
    color: white;
}

.send-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.voice-status {
    margin-top: 0.5rem;
    color: #e53e3e;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.curriculum-panel, .tools-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.curriculum-panel h3, .tools-panel h3 {
    color: #4a5568;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.topic-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.topic-item {
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #f7fafc;
}

.topic-item:hover {
    background: #667eea;
    color: white;
    transform: translateX(5px);
}

.topic-item i {
    color: #667eea;
    width: 20px;
}

.topic-item:hover i {
    color: white;
}

.tool-btn {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: none;
    border-radius: 10px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
}

.tool-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.tool-btn i {
    color: #667eea;
}

.tool-btn:hover i {
    color: white;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }
    
    .sidebar {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .curriculum-panel, .tools-panel {
        min-width: 250px;
    }
}
