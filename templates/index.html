<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MathMentor - 8th Grade Math Tutor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-calculator"></i> MathMentor</h1>
                <p>Your AI-powered 8th Grade California Math Tutor</p>
                <div class="stats" id="memoryStats">
                    <span id="messageCount">0 messages</span>
                </div>
            </div>
        </header>

        <div class="main-content">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant-message">
                        <div class="message-content">
                            <i class="fas fa-robot"></i>
                            <div class="text">
                                <p>Hi! I'm MathMentor, your 8th grade math teacher! 🧮</p>
                                <p><strong>How I teach:</strong></p>
                                <ul>
                                    <li>I'll ask you questions to guide your learning (like a real teacher!)</li>
                                    <li>I won't just give you answers - I'll help you discover them</li>
                                    <li>If you really need an answer, just say "I really need the answer"</li>
                                    <li>I'll create practice problems to help you learn</li>
                                    <li>I remember our entire conversation to track your progress</li>
                                </ul>
                                <p><strong>I can help you with:</strong></p>
                                <ul>
                                    <li>Number Systems & Irrational Numbers</li>
                                    <li>Expressions, Equations & Scientific Notation</li>
                                    <li>Functions & Linear Relationships</li>
                                    <li>Geometry & Pythagorean Theorem</li>
                                    <li>Statistics & Probability</li>
                                </ul>
                                <p><strong>Memory Commands:</strong></p>
                                <ul>
                                    <li><code>index_memory [topic]</code> - Search our conversation</li>
                                    <li><code>view_memory [start] [count]</code> - View conversation history</li>
                                    <li><code>clear_memory</code> - Clear conversation history</li>
                                </ul>
                                <p>You can type or click the microphone to speak! What math topic would you like to work on today?</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <input type="text" id="messageInput" placeholder="Ask me anything about 8th grade math..." maxlength="500">
                        <button id="voiceButton" class="voice-btn" title="Click to speak">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button id="sendButton" class="send-btn" title="Send message">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="voice-status" id="voiceStatus" style="display: none;">
                        <i class="fas fa-microphone-alt"></i>
                        <span>Listening...</span>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="curriculum-panel">
                    <h3><i class="fas fa-book"></i> 8th Grade Topics</h3>
                    <div class="topic-list">
                        <div class="topic-item" data-topic="number-system">
                            <i class="fas fa-infinity"></i>
                            <span>Number System</span>
                        </div>
                        <div class="topic-item" data-topic="expressions">
                            <i class="fas fa-superscript"></i>
                            <span>Expressions & Equations</span>
                        </div>
                        <div class="topic-item" data-topic="functions">
                            <i class="fas fa-chart-line"></i>
                            <span>Functions</span>
                        </div>
                        <div class="topic-item" data-topic="geometry">
                            <i class="fas fa-shapes"></i>
                            <span>Geometry</span>
                        </div>
                        <div class="topic-item" data-topic="statistics">
                            <i class="fas fa-chart-bar"></i>
                            <span>Statistics & Probability</span>
                        </div>
                    </div>
                </div>

                <div class="tools-panel">
                    <h3><i class="fas fa-tools"></i> Tools</h3>
                    <button class="tool-btn" id="searchBtn">
                        <i class="fas fa-search"></i>
                        Web Search
                    </button>
                    <button class="tool-btn" id="memoryBtn">
                        <i class="fas fa-brain"></i>
                        View Memory
                    </button>
                    <button class="tool-btn" id="clearBtn">
                        <i class="fas fa-trash"></i>
                        Clear Chat
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="loading" id="loading" style="display: none;">
        <div class="spinner"></div>
        <span>Thinking...</span>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
