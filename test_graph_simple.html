<!DOCTYPE html>
<html>
<head>
    <title>Graph Test</title>
</head>
<body>
    <h2>Testing Graph Rendering</h2>
    
    <div class="inline-graph">
        <h4>📊 Test Linear Function</h4>
        <div style="width: 500px; height: 350px; margin: 0 auto;">
            <canvas id="test_graph" width="500" height="350" style="border: 1px solid #ddd; background: white;"></canvas>
        </div>
        <script>
            setTimeout(function() {
                const canvas = document.getElementById('test_graph');
                if (!canvas) {
                    console.log('Canvas not found');
                    return;
                }
                
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.log('Context not found');
                    return;
                }
                
                const xPoints = [-2,-1,0,1,2];
                const yPoints = [-3,-1,1,3,5];
                const drawLine = true;
                
                console.log('Drawing graph with points:', xPoints, yPoints);
                
                // Clear canvas with white background
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Set up dimensions
                const padding = 50;
                const width = canvas.width - 2 * padding;
                const height = canvas.height - 2 * padding;
                
                // Calculate bounds
                const xMin = Math.min(...xPoints) - 1;
                const xMax = Math.max(...xPoints) + 1;
                const yMin = Math.min(...yPoints) - 1;
                const yMax = Math.max(...yPoints) + 1;
                
                // Helper functions
                function toCanvasX(x) {
                    return padding + (x - xMin) / (xMax - xMin) * width;
                }
                
                function toCanvasY(y) {
                    return padding + height - (y - yMin) / (yMax - yMin) * height;
                }
                
                // Draw grid
                ctx.strokeStyle = '#f0f0f0';
                ctx.lineWidth = 1;
                ctx.beginPath();
                for (let x = Math.ceil(xMin); x <= Math.floor(xMax); x++) {
                    const canvasX = toCanvasX(x);
                    ctx.moveTo(canvasX, padding);
                    ctx.lineTo(canvasX, padding + height);
                }
                for (let y = Math.ceil(yMin); y <= Math.floor(yMax); y++) {
                    const canvasY = toCanvasY(y);
                    ctx.moveTo(padding, canvasY);
                    ctx.lineTo(padding + width, canvasY);
                }
                ctx.stroke();
                
                // Draw axes
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                const yZero = toCanvasY(0);
                const xZero = toCanvasX(0);
                ctx.moveTo(padding, yZero);
                ctx.lineTo(padding + width, yZero);
                ctx.moveTo(xZero, padding);
                ctx.lineTo(xZero, padding + height);
                ctx.stroke();
                
                // Draw line
                if (drawLine && xPoints.length > 1) {
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(toCanvasX(xPoints[0]), toCanvasY(yPoints[0]));
                    for (let i = 1; i < xPoints.length; i++) {
                        ctx.lineTo(toCanvasX(xPoints[i]), toCanvasY(yPoints[i]));
                    }
                    ctx.stroke();
                }
                
                // Draw points
                ctx.fillStyle = '#667eea';
                for (let i = 0; i < xPoints.length; i++) {
                    const canvasX = toCanvasX(xPoints[i]);
                    const canvasY = toCanvasY(yPoints[i]);
                    ctx.beginPath();
                    ctx.arc(canvasX, canvasY, 4, 0, 2 * Math.PI);
                    ctx.fill();
                }
                
                console.log('Graph drawn successfully');
            }, 100);
        </script>
    </div>
    
    <p>If you see a graph above with a blue line and points, the rendering is working!</p>
</body>
</html>
