#!/usr/bin/env python3
"""
Test script to demonstrate the AI's graphing capabilities
"""

import requests
import json

def test_graphing():
    """Test the AI's ability to automatically use graphing tools"""
    
    # Test messages that should trigger graphing
    test_messages = [
        "Can you show me what the function y = 2x + 1 looks like?",
        "I want to see the graph of y = x^2",
        "Plot the points (1,2), (3,4), and (5,6) for me",
        "Show me a reflection of y = x across the x-axis"
    ]
    
    base_url = "http://localhost:5000"
    
    for message in test_messages:
        print(f"\n🧮 Testing: {message}")
        print("=" * 50)
        
        try:
            response = requests.post(f"{base_url}/chat", 
                                   json={"message": message},
                                   headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response: {data['response'][:200]}...")
                print(f"🔊 Should speak: {data.get('should_speak', True)}")
                
                # Check if response contains graph HTML
                if '<div class="inline-graph">' in data['response']:
                    print("📊 ✅ Graph generated successfully!")
                else:
                    print("📊 ❌ No graph found in response")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    print("🚀 Testing MathMentor's Graphing Capabilities")
    print("Make sure the app is running at http://localhost:5000")
    input("Press Enter to continue...")
    test_graphing()
