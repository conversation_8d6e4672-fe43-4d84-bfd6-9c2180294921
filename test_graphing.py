#!/usr/bin/env python3
"""
Test script to demonstrate the AI's graphing capabilities
"""

import requests
import json

def test_graphing():
    """Test the AI's ability to automatically use graphing tools"""
    
    # Test direct graphing commands
    direct_commands = [
        "generate_graph [-2,-1,0,1,2] [-3,-1,1,3,5] true Linear_Function blue",
        "generate_graph [1,2,3] [4,5,6] false Coordinate_Points red",
        "generate_graph [-3,-2,-1,0,1,2,3] [9,4,1,0,1,4,9] true Parabola green"
    ]

    # Test messages that should trigger AI to use graphing
    ai_messages = [
        "Can you show me what the function y = 2x + 1 looks like?",
        "I want to see the graph of y = x^2",
        "Plot the points (1,2), (3,4), and (5,6) for me"
    ]
    
    base_url = "http://localhost:5000"
    
    print("🔧 Testing Direct Graph Commands:")
    for message in direct_commands:
        print(f"\n🧮 Testing: {message}")
        print("=" * 50)

        try:
            response = requests.post(f"{base_url}/chat",
                                   json={"message": message},
                                   headers={"Content-Type": "application/json"})

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response received")
                print(f"🔊 Should speak: {data.get('should_speak', True)}")

                # Check if response contains graph HTML
                if '<div class="inline-graph">' in data['response']:
                    print("📊 ✅ Graph generated successfully!")
                else:
                    print("📊 ❌ No graph found in response")
            else:
                print(f"❌ Error: {response.status_code}")

        except Exception as e:
            print(f"❌ Connection error: {e}")

        print("-" * 50)

    print("\n🤖 Testing AI-Triggered Graphing:")
    for message in ai_messages:
        print(f"\n🧮 Testing: {message}")
        print("=" * 50)
        
        try:
            response = requests.post(f"{base_url}/chat", 
                                   json={"message": message},
                                   headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Response: {data['response'][:200]}...")
                print(f"🔊 Should speak: {data.get('should_speak', True)}")
                
                # Check if response contains graph HTML
                if '<div class="inline-graph">' in data['response']:
                    print("📊 ✅ Graph generated successfully!")
                else:
                    print("📊 ❌ No graph found in response")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    print("🚀 Testing MathMentor's Graphing Capabilities")
    print("Make sure the app is running at http://localhost:5000")
    input("Press Enter to continue...")
    test_graphing()
